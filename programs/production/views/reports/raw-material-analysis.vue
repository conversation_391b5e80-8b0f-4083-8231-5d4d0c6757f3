<template>
    <ui-view
        class="production-reports-raw-material-analysis"
        type="table"
        collection="production.raw-material-analysis"
        :columns="columns"
        :filters="filters"
        :get-rows="getRows"
        :left-panel-width="400"
        v-if="isInitialized"
    >
        <template slot="top-panel">
            <ui-scope
                id="production.reports.raw-material-analysis"
                :filters="scopeApplicableFilters"
                @changed="handleScopeChange"
            />
        </template>

        <template slot="left-panel">
            <ui-list
                ref="orderList"
                collection="production.orders"
                :extra-fields="['code', 'product.code', 'product.definition', 'product.image']"
                enable-search
                single-select
                style="background-color: white"
                :item-height="60"
                :html-template="getOrdersCellTemplate"
                @selected-items="handleSelect"
            />
        </template>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';

export default {
    data: () => ({
        isInitialized: false,
        scopeQuery: {},
        selected: null
    }),

    computed: {
        filters() {
            const filters = fastCopy(this.scopeQuery);

            if (_.isPlainObject(this.selected)) {
                filters.orderId = this.selected._id;
            }

            return filters;
        },
        scopeApplicableFilters() {
            const self = this;

            return [
                {code: 'today', label: 'Today', query: 'order.orderDate|today'},
                {code: 'yesterday', label: 'Yesterday', query: 'order.orderDate|yesterday'},
                {code: 'thisWeek', label: 'This week', query: 'order.orderDate|thisWeek'},
                {code: 'lastWeek', label: 'Last week', query: 'order.orderDate|lastWeek'},
                {code: 'thisMonth', label: 'This month', query: 'order.orderDate|thisMonth'},
                {code: 'lastMonth', label: 'Last month', query: 'order.orderDate|lastMonth'},
                {code: 'thisQuarter', label: 'This quarter', query: 'order.orderDate|thisQuarter'},
                {code: 'lastQuarter', label: 'Last quarter', query: 'order.orderDate|lastQuarter'},
                {field: 'order.code', label: 'Order Code'},
                {field: 'order.orderDate', code: 'orderDate', label: 'Order date', type: 'date'},
                {field: 'order.plannedStartDate', label: 'Planned start date', type: 'date'},
                {field: 'order.plannedEndDate', label: 'Planned end date', type: 'date'},
                {
                    field: 'order.branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {_id: {$in: this.$user.branchIds}, $sort: {name: 1}},
                    condition() {
                        return self.$setting('system.multiBranch');
                    }
                },
                {
                    field: 'order.warehouseId',
                    label: 'Warehouse',
                    collection: 'inventory.warehouses',
                    filters: {$sort: {name: 1}}
                },
                {
                    field: 'order.relatedPartnerId',
                    label: 'Related partner',
                    collection: 'kernel.partners'
                },
                {
                    field: 'order.status',
                    label: 'Order Status',
                    translateLabels: true,
                    items: [
                        {value: 'draft', label: 'Draft'},
                        {value: 'planning', label: 'Planning'},
                        {value: 'producing', label: 'Producing'},
                        {value: 'completed', label: 'Completed'},
                        {value: 'canceled', label: 'Canceled'}
                    ]
                },
                {
                    field: 'status',
                    label: 'Task Status',
                    translateLabels: true,
                    items: [
                        {value: 'draft', label: 'Draft'},
                        {value: 'waiting', label: 'Waiting'},
                        {value: 'in-progress', label: 'In Progress'},
                        {value: 'stopped', label: 'Stopped'},
                        {value: 'awaiting-procurement', label: 'Awaiting Procurement'},
                        {value: 'partially-completed', label: 'Partially Completed'},
                        {value: 'completed', label: 'Completed'},
                        {value: 'canceled', label: 'Canceled'}
                    ]
                }
            ];
        },
        columns() {
            const self = this;

            return [
                {
                    field: 'order.code',
                    label: 'Order Code',
                    width: 120,
                    relationParams(params) {
                        const data = params.data;
                        return {
                            id: data.orderId,
                            view: 'production.production.orders'
                        };
                    }
                },
                {
                    field: 'order.orderDate',
                    label: 'Order Date',
                    format: 'date',
                    sort: 'desc',
                    width: 120
                },
                {
                    field: 'order.product.code',
                    label: 'Order Product Code',
                    subSelect: ['code'],
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};
                        relation.isVisible = _.isObject(data.order) && _.isObject(data.order.product) && _.isString(data.order.product.code);
                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products';
                            relation.id = data.order.productId;
                            relation.template = '{{code}}';
                        }
                        return relation;
                    },
                    width: 150
                },
                {
                    field: 'order.product.definition',
                    label: 'Order Product Definition',
                    subSelect: ['definition'],
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};
                        relation.isVisible = _.isObject(data.order) && _.isObject(data.order.product) && _.isString(data.order.product.definition);
                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products';
                            relation.id = data.order.productId;
                            relation.template = '{{definition}}';
                        }
                        return relation;
                    },
                    minWidth: 210
                },
                {
                    field: 'productCode',
                    label: 'Raw Material Code',
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};
                        relation.isVisible = _.isString(data.productId);
                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products';
                            relation.id = data.productId;
                            relation.template = data.productCode;
                        }
                        return relation;
                    },
                    width: 150
                },
                {
                    field: 'productDefinition',
                    label: 'Raw Material Definition',
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};
                        relation.isVisible = _.isString(data.productId);
                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products';
                            relation.id = data.productId;
                            relation.template = data.productDefinition;
                        }
                        return relation;
                    },
                    minWidth: 210
                },
                {
                    field: 'requiredDate',
                    label: 'Required Date',
                    format: 'datetime',
                    width: 155
                },
                {
                    field: 'plannedQuantity',
                    label: 'Required Quantity',
                    width: 120,
                    format: 'unit',
                    formatOptions() {
                        return {number: {precision: self.$setting('system.unitPrecision')}};
                    }
                },
                {
                    field: 'actualQuantity',
                    label: 'Actual Quantity',
                    width: 120,
                    format: 'unit',
                    formatOptions() {
                        return {number: {precision: self.$setting('system.unitPrecision')}};
                    }
                },
                {
                    field: 'unitName',
                    label: 'Unit',
                    width: 90
                },
                {
                    field: 'backflush',
                    label: 'Backflush',
                    width: 100,
                    render: params => {
                        return params.data.backflush ? this.$t('Yes') : this.$t('No');
                    }
                }
            ];
        }
    },

    methods: {
        getOrdersCellTemplate(item) {
            const imageUrl = item.product && item.product.image
                ? this.$app.absoluteUrl(`files/${item.product.image}`)
                : this.$app.absoluteUrl('static/images/no-image.png');

            return `
            <div class="production-order-cell">
                <div class="order-cell-container">
                    <img class="order-cell-image" src="${imageUrl}" />
                </div>
                <div class="order-cell-content">
                    <div class="order-cell-code">${item.code}</div>
                    <div class="order-cell-product">
                        ${item.product ? item.product.definition || item.product.code : 'N/A'}
                    </div>
                </div>
            </div>
            `.trim();
        },
        async getRows(payload) {
            const query = fastCopy(this.filters);
            const limit = payload.$limit || 75;
            const skip = payload.$skip || 0;

            return await this.$rpc('production.get-raw-material-analysis', {
                query,
                limit,
                skip
            });
        },
        handleScopeChange(model) {
            this.scopeQuery = model.query;
        },
        handleSelect(selected) {
            this.selected = selected[0];
        }
    },

    async created() {
        this.isInitialized = true;
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.production-reports-raw-material-analysis {
    .ui-content {
        display: flex;
        flex-flow: column nowrap;
    }

    .order-items {
        flex: 0 0 100%;
    }
}

.production-order-cell {
    display: flex;
    padding: 10px;

    .order-cell-container {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
    }

    .order-cell-image {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }

    .order-cell-content {
        display: flex;
        flex-flow: column;
        justify-content: center;
        flex: 1;
        margin-left: 10px;
        min-width: 0;
    }

    .order-cell-code {
        width: 100%;
        overflow: hidden;
        font-weight: 700;
        @include text-truncate();
        font-size: 14px;
        line-height: 1;
        margin-bottom: 5px;
    }

    .order-cell-product {
        width: 100%;
        overflow: hidden;
        @include text-truncate();
        font-size: 12px;
        color: #7f878a;
        line-height: 1;
    }
}
</style>
