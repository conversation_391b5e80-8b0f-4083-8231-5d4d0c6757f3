<template>
    <ui-view
        class="production-reports-raw-material-analysis"
        type="table"
        collection="production.tasks"
        :process-result="processResult"
        :columns="columns"
        :filters="taskFilters"
        :extra-fields="['_id', 'orderId', 'products']"
        :summary-row="summaryRow"
        :left-panel-width="400"
        v-if="isInitialized"
    >
        <template slot="top-panel">
            <ui-scope
                id="production.reports.raw-material-analysis"
                :filters="scopeApplicableFilters"
                @changed="handleScopeChange"
            />
        </template>

        <template slot="left-panel">
            <ui-list
                ref="orderList"
                collection="production.orders"
                :extra-fields="['code', 'product.code', 'product.definition', 'product.image']"
                enable-search
                single-select
                style="background-color: white"
                :item-height="60"
                :html-template="getOrdersCellTemplate"
                @selected-items="handleSelect"
            />
        </template>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';

export default {
    data: () => ({
        isInitialized: false,
        scopeQuery: {},
        selected: null
    }),

    computed: {
        taskFilters() {
            const filters = fastCopy(this.scopeQuery);

            if (_.isPlainObject(this.selected)) {
                filters.orderId = this.selected._id;
            }

            return filters;
        },
        scopeApplicableFilters() {
            const self = this;

            return [
                {code: 'today', label: 'Today', query: 'order.orderDate|today'},
                {
                    code: 'yesterday',
                    label: 'Yesterday',
                    query: 'order.orderDate|yesterday'
                },
                {
                    code: 'thisWeek',
                    label: 'This week',
                    query: 'order.orderDate|thisWeek'
                },
                {
                    code: 'lastWeek',
                    label: 'Last week',
                    query: 'order.orderDate|lastWeek'
                },
                {
                    code: 'thisMonth',
                    label: 'This month',
                    query: 'order.orderDate|thisMonth'
                },
                {
                    code: 'lastMonth',
                    label: 'Last month',
                    query: 'order.orderDate|lastMonth'
                },
                {
                    code: 'thisQuarter',
                    label: 'This quarter',
                    query: 'order.orderDate|thisQuarter'
                },
                {
                    code: 'lastQuarter',
                    label: 'Last quarter',
                    query: 'order.orderDate|lastQuarter'
                },

                {field: 'order.code', label: 'Order Code'},
                {
                    field: 'order.orderDate',
                    code: 'orderDate',
                    label: 'Order date',
                    type: 'date'
                },
                {
                    field: 'order.plannedStartDate',
                    label: 'Planned start date',
                    type: 'date'
                },
                {
                    field: 'order.plannedEndDate',
                    label: 'Planned end date',
                    type: 'date'
                },
                {
                    field: 'order.branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {
                        _id: {$in: this.$user.branchIds},
                        $sort: {name: 1}
                    },
                    condition() {
                        return self.$setting('system.multiBranch');
                    }
                },
                {
                    field: 'order.warehouseId',
                    label: 'Warehouse',
                    collection: 'inventory.warehouses',
                    filters: {$sort: {name: 1}}
                },
                {
                    field: 'order.relatedPartnerId',
                    label: 'Related partner',
                    collection: 'kernel.partners'
                },
                {
                    field: 'order.status',
                    label: 'Order Status',
                    translateLabels: true,
                    items: [
                        {value: 'draft', label: 'Draft'},
                        {value: 'planning', label: 'Planning'},
                        {value: 'producing', label: 'Producing'},
                        {value: 'completed', label: 'Completed'},
                        {value: 'canceled', label: 'Canceled'}
                    ]
                },
                {
                    field: 'status',
                    label: 'Task Status',
                    translateLabels: true,
                    items: [
                        {value: 'draft', label: 'Draft'},
                        {value: 'waiting', label: 'Waiting'},
                        {value: 'in-progress', label: 'In Progress'},
                        {value: 'stopped', label: 'Stopped'},
                        {value: 'awaiting-procurement', label: 'Awaiting Procurement'},
                        {value: 'partially-completed', label: 'Partially Completed'},
                        {value: 'completed', label: 'Completed'},
                        {value: 'canceled', label: 'Canceled'}
                    ]
                }
            ];
        },
        columns() {
            const self = this;

            return [
                {
                    field: 'order.code',
                    label: 'Order Code',
                    width: 120,
                    relationParams(params) {
                        const data = params.data;

                        return {
                            id: data.orderId,
                            view: 'production.production.orders'
                        };
                    }
                },
                {
                    field: 'order.orderDate',
                    label: 'Order Date',
                    format: 'date',
                    sort: 'desc',
                    width: 120
                },
                {
                    field: 'order.product.code',
                    label: 'Order Product Code',
                    subSelect: ['code'],
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isObject(data.order) && _.isObject(data.order.product) && _.isString(data.order.product.code);

                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products';
                            relation.id = data.order.productId;
                            relation.template = '{{code}}';
                        }

                        return relation;
                    },
                    width: 150
                },
                {
                    field: 'order.product.definition',
                    label: 'Order Product Definition',
                    subSelect: ['definition'],
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isObject(data.order) && _.isObject(data.order.product) && _.isString(data.order.product.definition);

                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products';
                            relation.id = data.order.productId;
                            relation.template = '{{definition}}';
                        }

                        return relation;
                    },
                    minWidth: 210
                },
                {
                    field: 'productCode',
                    label: 'Raw Material Code',
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isString(data.productId);

                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products';
                            relation.id = data.productId;
                            relation.template = data.productCode;
                        }

                        return relation;
                    },
                    width: 150
                },
                {
                    field: 'productDefinition',
                    label: 'Raw Material Definition',
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isString(data.productId);

                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products';
                            relation.id = data.productId;
                            relation.template = data.productDefinition;
                        }

                        return relation;
                    },
                    minWidth: 210
                },
                {
                    field: 'requiredDate',
                    label: 'Required Date',
                    format: 'datetime',
                    width: 155
                },
                {
                    field: 'plannedQuantity',
                    label: 'Required Quantity',
                    width: 120,
                    format: 'unit',
                    formatOptions() {
                        return {number: {precision: self.$setting('system.unitPrecision')}};
                    }
                },
                {
                    field: 'actualQuantity',
                    label: 'Actual Quantity',
                    width: 120,
                    format: 'unit',
                    formatOptions() {
                        return {number: {precision: self.$setting('system.unitPrecision')}};
                    }
                },
                {
                    field: 'unitName',
                    label: 'Unit',
                    width: 90
                },
                {
                    field: 'backflush',
                    label: 'Backflush',
                    width: 100,
                    render: params => {
                        return params.data.backflush ? this.$t('Yes') : this.$t('No');
                    }
                },
                {
                    field: 'plannedCost',
                    label: 'Planned Cost',
                    width: 120,
                    format: 'currency',
                    hidden: () =>
                        !this.$checkPermission({
                            type: 'permission',
                            name: 'system.canSeeCosts'
                        })
                },
                {
                    field: 'actualCost',
                    label: 'Actual Cost',
                    width: 120,
                    format: 'currency',
                    hidden: () =>
                        !this.$checkPermission({
                            type: 'permission',
                            name: 'system.canSeeCosts'
                        })
                },
                {
                    field: 'order.branch.name',
                    label: 'Branch Office',
                    hidden: !this.$setting('system.multiBranch'),
                    width: 180,
                    relationParams(params) {
                        const data = params.data;

                        if (_.isPlainObject(data.order) && _.isPlainObject(data.order.branch) && _.isString(data.order.branch._id)) {
                            return {
                                id: data.order.branch._id,
                                view: 'system.management.configuration.branches'
                            };
                        }

                        return {};
                    }
                },
                {
                    field: 'order.warehouse.name',
                    label: 'Warehouse',
                    width: 180,
                    relationParams(params) {
                        const data = params.data;

                        if (_.isPlainObject(data.order) && _.isPlainObject(data.order.warehouse) && _.isString(data.order.warehouse._id)) {
                            return {
                                id: data.order.warehouse._id,
                                view: 'inventory.configuration.warehouses'
                            };
                        }

                        return {};
                    }
                },
                {
                    field: 'order.status',
                    label: 'Order Status',
                    tagsCell: true,
                    translateLabels: true,
                    tagLabels: [
                        {value: 'draft', label: 'Draft', color: 'default'},
                        {value: 'planning', label: 'Planning', color: 'teal'},
                        {value: 'producing', label: 'Producing', color: 'success'},
                        {value: 'completed', label: 'Completed', color: 'primary'},
                        {value: 'canceled', label: 'Canceled', color: 'danger'}
                    ],
                    width: 150
                }
            ];
        }
    },

    methods: {
        getOrdersCellTemplate(item) {
            const imageUrl = item.product && item.product.image
                ? this.$app.absoluteUrl(`files/${item.product.image}`)
                : this.$app.absoluteUrl('static/images/no-image.png');

            return `
            <div class="production-order-cell">
                <div class="order-cell-container">
                    <img class="order-cell-image" src="${imageUrl}" />
                </div>
                <div class="order-cell-content">
                    <div class="order-cell-code">${item.code}</div>
                    <div class="order-cell-product">
                        ${item.product ? item.product.definition || item.product.code : 'N/A'}
                    </div>
                </div>
            </div>
            `.trim();
        },
        async handleScopeChange(model) {
            this.scopeQuery = model.query;
        },
        async handleSelect(selected) {
            this.selected = selected = selected[0];
        },
        async processResult(result) {
            return new Promise((resolve) => {
                setTimeout(async () => {
                    try {
                        const processedData = [];

                        // Get all unique product IDs first
                        const productIds = [];
                        for (const task of result.data) {
                            const products = task.products || [];
                            for (const taskProduct of products) {
                                if (taskProduct.itemType === 'product' && taskProduct.productId && !productIds.includes(taskProduct.productId)) {
                                    productIds.push(taskProduct.productId);
                                }
                            }
                        }

                        // Fetch all products at once
                        let productsMap = {};
                        if (productIds.length > 0) {
                            const products = await this.$collection('inventory.products').find({
                                _id: {$in: productIds},
                                $select: ['code', 'definition', 'barcode']
                            });
                            productsMap = _.keyBy(products, '_id');
                        }

                        for (const task of result.data) {
                            // Get task products (BOM items)
                            const products = task.products || [];

                            for (const taskProduct of products) {
                                if (taskProduct.itemType !== 'product') {
                                    continue;
                                }

                                const product = productsMap[taskProduct.productId];

                                const bomItem = {
                                    _id: `${task._id}-${taskProduct.productId}`,
                                    orderId: task.orderId,
                                    taskId: task._id,
                                    productId: taskProduct.productId,
                                    product: product,
                                    productCode: taskProduct.code || (product ? product.code : ''),
                                    productDefinition: taskProduct.title || (product ? product.definition : ''),
                                    plannedQuantity: taskProduct.plannedQuantity || 0,
                                    actualQuantity: taskProduct.actualQuantity || 0,
                                    plannedCost: taskProduct.plannedCost || 0,
                                    actualCost: taskProduct.actualCost || 0,
                                    unitId: taskProduct.unitId,
                                    unitName: taskProduct.unitName,
                                    requiredDate: task.plannedStartDate,
                                    backflush: !!taskProduct.backflush,
                                    order: task.order
                                };

                                processedData.push(bomItem);
                            }
                        }

                        result.data = processedData;
                        result.total = processedData.length;

                        resolve(result);
                    } catch (error) {
                        console.error('Error in processResult:', error);
                        resolve(result);
                    }
                }, 0);
            });
        },
        async summaryRow(rows) {
            return new Promise((resolve) => {
                setTimeout(async () => {
                    try {
                        const totals = {
                            plannedQuantity: 0,
                            actualQuantity: 0,
                            plannedCost: 0,
                            actualCost: 0,
                            count: 0
                        };

                        let dataToSum = rows;
                        if (!dataToSum || dataToSum.length === 0) {
                            const result = await this.$collection('production.tasks').find(this.taskFilters);
                            const processedResult = await this.processResult({data: result, total: result.length});
                            dataToSum = processedResult.data;
                        }

                        dataToSum.forEach(row => {
                            totals.plannedQuantity += _.isNumber(row.plannedQuantity) ? row.plannedQuantity : 0;
                            totals.actualQuantity += _.isNumber(row.actualQuantity) ? row.actualQuantity : 0;
                            totals.plannedCost += _.isNumber(row.plannedCost) ? row.plannedCost : 0;
                            totals.actualCost += _.isNumber(row.actualCost) ? row.actualCost : 0;
                            totals.count += 1;
                        });

                        resolve({
                            'order.code': this.$t('TOTAL'),
                            plannedQuantity: totals.plannedQuantity,
                            actualQuantity: totals.actualQuantity,
                            plannedCost: totals.plannedCost,
                            actualCost: totals.actualCost,
                            productCode: `${totals.count} ${this.$t('items')}`
                        });
                    } catch (error) {
                        console.error('Error in summaryRow:', error);
                        resolve({
                            'order.code': this.$t('TOTAL'),
                            productCode: '0 items'
                        });
                    }
                }, 0);
            });
        }
    },

    async created() {
        this.isInitialized = true;
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.production-reports-raw-material-analysis {
    .ui-content {
        display: flex;
        flex-flow: column nowrap;
    }

    .order-items {
        flex: 0 0 100%;
    }
}

.production-order-cell {
    display: flex;
    padding: 10px;

    .order-cell-container {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
    }

    .order-cell-image {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }

    .order-cell-content {
        display: flex;
        flex-flow: column;
        justify-content: center;
        flex: 1;
        margin-left: 10px;
        min-width: 0;
    }

    .order-cell-code {
        width: 100%;
        overflow: hidden;
        font-weight: 700;
        @include text-truncate();
        font-size: 14px;
        line-height: 1;
        margin-bottom: 5px;
    }

    .order-cell-product {
        width: 100%;
        overflow: hidden;
        @include text-truncate();
        font-size: 12px;
        color: #7f878a;
        line-height: 1;
    }
}
</style>
