import _ from 'lodash';
import initApi from './api/init';
import reports from './methods/reports';

export default async function (app) {
    initApi(app);

    // Register RPC methods
    app.rpc(reports);

    // Sync procurement item statuses.
    app.collection('inventory.transfers').hooks({
        after: {
            update: [syncProcurementItemStatuses],
            patch: [syncProcurementItemStatuses]
        }
    });
}

// await app.db.collection('production_request').deleteMany({});
// await app.db.collection('production_operations').deleteMany({});
// await app.db.collection('production_procurement-points').deleteMany({});
// await app.db.collection('production_procurement-point-items').deleteMany({});
// await app.db.collection('production_tasks').deleteMany({});
// await app.db.collection('production_orders').deleteMany({});
// await app.db.collection('production_order-items').deleteMany({});

// await app.db.collection('inventory_transfers').deleteMany({});
// await app.db.collection('inventory_transfer-sub-items').deleteMany({});
// await app.db.collection('inventory_reservations').deleteMany({});
// await app.db.collection('pinventory_external-reservations').deleteMany({});

// await app.db.collection('production_eco').deleteMany({});
// await app.db.collection('production_models').deleteMany({});

async function syncProcurementItemStatuses(context) {
    const app = context.app;
    const transfers = Array.isArray(context.result) ? context.result : [context.result];
    const transferIds = [];
    const procurementItemsOperations = [];
    const tasksOperations = [];

    for (const transfer of transfers) {
        if (
            transfer.documentType === 'production-transfers' &&
            transfer.type === 'outgoing' &&
            transfer.status === 'approved'
        ) {
            transferIds.push(transfer._id);
        }
    }

    if (transferIds.length > 0) {
        const now = app.datetime.local().toJSDate();
        const procurementItems = await app.collection('production.procurement-point-items').find({
            status: 'waiting',
            transferIds: {$in: transferIds}
        });
        const tasks = await app.collection('production.tasks').find({
            _id: {$in: _.uniq(procurementItems.map(procurementItem => procurementItem.taskId))},
            $select: ['_id', 'completedQuantity']
        });
        const tasksMap = {};
        for (const task of tasks) {
            tasksMap[task._id] = task;
        }

        for (const procurementItem of procurementItems) {
            const task = tasksMap[procurementItem.taskId];

            const transferItems = [];
            for (const transfer of transfers) {
                if ((procurementItem.transferIds ?? []).includes(transfer._id)) {
                    for (const transferItem of transfer.items ?? []) {
                        if (transferItem.productId === procurementItem.productId) {
                            transferItems.push(transferItem);
                        }
                    }
                }
            }

            let suppliedQuantity = 0;
            for (const transferItem of transferItems) {
                suppliedQuantity += transferItem.actualQty ?? 0;
            }

            procurementItemsOperations.push({
                updateOne: {
                    filter: {_id: procurementItem._id},
                    update: {
                        $set: {
                            status: 'completed',
                            suppliedDate: now,
                            suppliedQuantity: app.round(
                                (procurementItem.suppliedQuantity ?? 0) + suppliedQuantity,
                                'unit'
                            )
                        }
                    }
                }
            });
            tasksOperations.push({
                updateOne: {
                    filter: {_id: procurementItem.taskId},
                    update: {
                        $set: {
                            status: task.completedQuantity > 0 ? 'partially-completed' : 'waiting',
                            startFeasibility: 'feasible'
                        }
                    }
                }
            });
        }
    }

    if (procurementItemsOperations.length > 0) {
        await app.collection('production.procurement-point-items').bulkWrite(procurementItemsOperations);
    }
    if (tasksOperations.length > 0) {
        await app.collection('production.tasks').bulkWrite(tasksOperations);
    }

    return context;
}
