export default {
    name: 'get-raw-material-analysis',
    async action(payload) {
        const app = this.app;
        const {query = {}, limit = 75, skip = 0} = payload;

        try {
            // Get tasks based on filters
            const tasks = await app.collection('production.tasks').find({
                ...query,
                $limit: limit,
                $skip: skip,
                $populate: [
                    {field: 'order', query: {$select: ['code', 'orderDate', 'productId', 'status', 'branchId', 'warehouseId']}},
                    {field: 'order.product', query: {$select: ['code', 'definition']}},
                    {field: 'order.branch', query: {$select: ['name']}},
                    {field: 'order.warehouse', query: {$select: ['name']}}
                ]
            });

            const processedData = [];

            // Get all unique product IDs from task products
            const productIds = [];
            for (const task of tasks) {
                const products = task.products || [];
                for (const taskProduct of products) {
                    if (taskProduct.itemType === 'product' && taskProduct.productId && !productIds.includes(taskProduct.productId)) {
                        productIds.push(taskProduct.productId);
                    }
                }
            }

            // Fetch all products at once
            let productsMap = {};
            if (productIds.length > 0) {
                const products = await app.collection('inventory.products').find({
                    _id: {$in: productIds},
                    $select: ['code', 'definition', 'barcode']
                });
                productsMap = app._.keyBy(products, '_id');
            }

            // Process each task
            for (const task of tasks) {
                const products = task.products || [];

                for (const taskProduct of products) {
                    if (taskProduct.itemType !== 'product') {
                        continue;
                    }

                    const product = productsMap[taskProduct.productId];

                    const bomItem = {
                        _id: `${task._id}-${taskProduct.productId}`,
                        orderId: task.orderId,
                        taskId: task._id,
                        productId: taskProduct.productId,
                        product: product,
                        productCode: taskProduct.code || (product ? product.code : ''),
                        productDefinition: taskProduct.title || (product ? product.definition : ''),
                        plannedQuantity: taskProduct.plannedQuantity || 0,
                        actualQuantity: taskProduct.actualQuantity || 0,
                        plannedCost: taskProduct.plannedCost || 0,
                        actualCost: taskProduct.actualCost || 0,
                        unitId: taskProduct.unitId,
                        unitName: taskProduct.unitName,
                        requiredDate: task.plannedStartDate,
                        backflush: !!taskProduct.backflush,
                        order: task.order
                    };

                    processedData.push(bomItem);
                }
            }

            // Get total count for pagination
            const totalTasks = await app.collection('production.tasks').count(query);
            
            // Estimate total items (rough calculation)
            const avgItemsPerTask = processedData.length / Math.max(tasks.length, 1);
            const estimatedTotal = Math.round(totalTasks * avgItemsPerTask);

            return {
                data: processedData,
                total: estimatedTotal
            };

        } catch (error) {
            console.error('Error in get-raw-material-analysis:', error);
            return {
                data: [],
                total: 0
            };
        }
    }
};
